using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.ListTickets;

public record ListTicketsQuery : IRequest<PagedResult<TicketListItemDto>>
{
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public string? SearchTerm { get; init; }
    public string? Title { get; init; }
    public Guid? SubjectId { get; init; }
    public Guid? CustomerId { get; init; }
    public Guid? UserId { get; init; }
    public PriorityEnum? Priority { get; init; }
    public Guid? StatusId { get; init; }
    public DateTime? StartDate { get; init; }
    public DateTime? EndDate { get; init; }
    public Guid[]? DepartmentIds { get; init; } = null;
}

public record TicketListItemDto
{
    public Guid Id { get; init; }
    public Guid SubjectId { get; init; }
    public string? SubjectName { get; init; }
    public Guid CustomerId { get; init; }
    public string? CustomerName { get; init; }
    public string Title { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public Guid? NotificationWayId { get; init; }
    public string? NotificationWay { get; init; } = string.Empty;
    public Guid? UserId { get; init; }
    public string? UserName { get; init; }
    public Guid ReporterUserId { get; init; }
    public string? ReporterUserName { get; init; }
    public PriorityEnum Priority { get; init; }
    public Guid StatusId { get; init; }
    public string? StatusName { get; init; } = string.Empty;
    public DateTime? EndDate { get; init; }
    public DateTime InsertDate { get; init; }
    public int CommentCount { get; init; }
    public List<DepartmentDTO> Departments { get; set; }
}

public record DepartmentDTO
{
    public Guid DepartmentId { get; set; }
    public string Name { get; set; }
}
