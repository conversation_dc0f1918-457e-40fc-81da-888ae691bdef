using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Tickets.GetAvailableTransitions;

internal sealed class GetAvailableTransitionsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/tickets/{ticketId:guid}/available-transitions", async (
            Guid ticketId,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetAvailableTransitionsQuery(ticketId);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Tickets")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Tickets");
    }
}
