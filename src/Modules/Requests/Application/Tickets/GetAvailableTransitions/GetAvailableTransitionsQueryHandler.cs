using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Requests.Domain.Enums;
using Shared.Application;

namespace Requests.Application.Tickets.GetAvailableTransitions;

public class GetAvailableTransitionsQueryHandler(
    IRequestsDbContext context) : IRequestHandler<GetAvailableTransitionsQuery, Result<List<AvailableTransitionDto>>>
{
    public async Task<Result<List<AvailableTransitionDto>>> Handle(
        GetAvailableTransitionsQuery request,
        CancellationToken cancellationToken)
    {
        // Ticket'ı ve ilişkili verileri getir
        var ticket = await context.Tickets
            .Include(t => t.Subject)
                .ThenInclude(s => s!.Flow)
            .Include(t => t.Status)
            .FirstOrDefaultAsync(t => t.Id == request.TicketId, cancellationToken);

        if (ticket == null)
        {
            return Result.Failure<List<AvailableTransitionDto>>(
                Error.NotFound("Ticket.NotFound", "Ticket bulunamadı"));
        }

        // Eğer ticket'ın subject'i flow'a bağlı değilse boş liste dön
        if (ticket.Subject?.Flow == null)
        {
            return Result.Success(new List<AvailableTransitionDto>());
        }

        // Eğer ticket'ın mevcut node'u yoksa, flow'un başlangıç node'unu bul
        if (ticket.StatusId == null)
        {
            var startNode = await context.Nodes
                .Where(n => n.FlowId == ticket.Subject.FlowId && n.NodeType == NodeType.Start)
                .FirstOrDefaultAsync(cancellationToken);

            if (startNode == null)
            {
                return Result.Success(new List<AvailableTransitionDto>());
            }

            // Ticket'ın current node'unu güncelle
            ticket.StatusId = startNode.Id;
            ticket.Status = startNode;
            await context.SaveChangesAsync(cancellationToken);
        }

        // Mevcut node'dan çıkan transition'ları getir
        var availableTransitions = await context.Transitions
            .Include(t => t.FromNode)
            .Include(t => t.ToNode)
            .Where(t => t.FromNodeId == ticket.StatusId)
            .Select(t => new AvailableTransitionDto(
                t.Id,
                t.Name,
                t.FromNodeId,
                t.FromNode.Name,
                t.ToNodeId,
                t.ToNode.Name,
                t.ToNode.Description,
                true, // Şimdilik tüm transition'lar izinli
                null
            ))
            .ToListAsync(cancellationToken);

        return Result.Success(availableTransitions);
    }
}
